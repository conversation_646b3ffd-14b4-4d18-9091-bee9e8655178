<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Hello - Synology DSM 7.2</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎉 Simple Hello World</h1>
            <p class="subtitle">群晖 DSM 7.2 插件测试</p>
        </header>
        
        <main>
            <div class="card">
                <h2>欢迎使用 Simple Hello 插件！</h2>
                <p>这是一个为群晖 DSM 7.2 设计的简单测试插件。</p>
                
                <div class="features">
                    <div class="feature">
                        <h3>✅ 非 Root 权限</h3>
                        <p>使用普通用户权限运行，安全可靠</p>
                    </div>
                    
                    <div class="feature">
                        <h3>🚀 轻量级</h3>
                        <p>最小化依赖，快速启动</p>
                    </div>
                    
                    <div class="feature">
                        <h3>🔧 易于测试</h3>
                        <p>简单的 HTTP 服务器，便于验证功能</p>
                    </div>
                </div>
                
                <div class="info">
                    <h3>系统信息</h3>
                    <ul>
                        <li><strong>插件版本:</strong> 1.0.0</li>
                        <li><strong>兼容系统:</strong> DSM 7.0 - 7.2</li>
                        <li><strong>服务端口:</strong> 8080</li>
                        <li><strong>运行用户:</strong> 非 root</li>
                    </ul>
                </div>
            </div>
        </main>
        
        <footer>
            <p>基于 SynoCommunity/spksrc 框架构建</p>
            <p class="timestamp">页面加载时间: <span id="timestamp"></span></p>
        </footer>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
        
        // 简单的状态检查
        setInterval(() => {
            document.querySelector('.container').style.opacity = '1';
        }, 100);
    </script>
</body>
</html>
