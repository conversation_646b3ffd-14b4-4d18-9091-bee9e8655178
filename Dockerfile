# Dockerfile for SynoCommunity spksrc-style build environment
# Based on Debian 12 for DSM 7.2 compatibility

FROM debian:12-slim

# Install basic build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    make \
    autoconf \
    automake \
    libtool \
    pkg-config \
    python3 \
    python3-pip \
    tar \
    gzip \
    zip \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /spksrc

# Set environment variables
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Default command
CMD ["/bin/bash"]
