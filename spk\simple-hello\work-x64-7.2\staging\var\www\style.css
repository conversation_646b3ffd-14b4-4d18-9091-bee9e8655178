/* Simple Hello World CSS for Synology DSM 7.2 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.9;
}

main {
    padding: 40px 30px;
}

.card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.8em;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.feature {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #4facfe;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.feature h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.feature p {
    color: #666;
    font-size: 0.95em;
}

.info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.info h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.info ul {
    list-style: none;
}

.info li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
    color: #555;
}

.info li:last-child {
    border-bottom: none;
}

.info strong {
    color: #2c3e50;
    font-weight: 600;
}

footer {
    background: #2c3e50;
    color: white;
    padding: 20px 30px;
    text-align: center;
    font-size: 0.9em;
}

.timestamp {
    margin-top: 10px;
    opacity: 0.8;
    font-size: 0.85em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 8px;
    }
    
    header {
        padding: 30px 20px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    main {
        padding: 30px 20px;
    }
    
    .card {
        padding: 20px;
    }
    
    .features {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
