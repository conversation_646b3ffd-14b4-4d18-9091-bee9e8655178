#!/usr/bin/env python3
"""
Simple Hello World HTTP Server for Synology DSM 7.2
Runs on non-root user to avoid permission issues
"""

import http.server
import socketserver
import os
import sys
import signal
import logging
from pathlib import Path

# Configuration
PORT = 8080
WEB_DIR = "/var/packages/simple-hello/target/var/www"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/simple-hello.log'),
        logging.StreamHandler()
    ]
)

class SimpleHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=WEB_DIR, **kwargs)
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logging.info(format % args)

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logging.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)

def main():
    """Main server function"""
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # Check if web directory exists
    if not os.path.exists(WEB_DIR):
        logging.error(f"Web directory {WEB_DIR} does not exist")
        sys.exit(1)
    
    # Start server
    try:
        with socketserver.TCPServer(("", PORT), SimpleHTTPRequestHandler) as httpd:
            logging.info(f"Simple Hello server starting on port {PORT}")
            logging.info(f"Serving files from {WEB_DIR}")
            httpd.serve_forever()
    except PermissionError:
        logging.error(f"Permission denied to bind to port {PORT}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
