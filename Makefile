# Main Makefile for SynoCommunity spksrc-style build
# Based on SynoCommunity/spksrc framework

# Default architecture for DSM 7.2
DEFAULT_ARCH = x64-7.2

# Setup development environment
setup:
	@echo "Setting up development environment..."
	@mkdir -p packages
	@mkdir -p distrib
	@mkdir -p toolchain
	@echo "Setup complete"

# Clean all build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf packages/*
	@rm -rf spk/*/work-*
	@echo "Clean complete"

# Build all supported architectures
all-supported:
	@echo "Building for all supported architectures..."
	$(MAKE) -C spk/simple-hello arch-x64-7.2
	$(MAKE) -C spk/simple-hello arch-aarch64-7.2

# Build specific architecture
arch-%:
	$(MAKE) -C spk/simple-hello $@

# Help target
help:
	@echo "Available targets:"
	@echo "  setup         - Setup development environment"
	@echo "  clean         - Clean build artifacts"
	@echo "  all-supported - Build for all supported architectures"
	@echo "  arch-x64-7.2  - Build for x64 DSM 7.2"
	@echo "  arch-aarch64-7.2 - Build for aarch64 DSM 7.2"

.PHONY: setup clean all-supported help
