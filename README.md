# 群晖 DSM 7.2 插件项目

这是一个基于 SynoCommunity/spksrc 框架的群晖 DSM 7.2 插件项目，专门设计为避免任何 root 权限操作。

## 项目特点

- ✅ **非 Root 权限**: 完全避免 root 权限操作，提高安全性
- ✅ **DSM 7.2 兼容**: 专为群晖 DSM 7.0-7.2 设计
- ✅ **轻量级**: 最小化依赖，快速部署
- ✅ **易于测试**: 简单的 HTTP 服务器，便于验证功能

## 项目结构

```
charpqunhuiplugin/
├── Makefile                    # 主构建文件
├── Dockerfile                  # Docker 编译环境
├── README.md                   # 项目说明
├── mk/
│   └── spksrc.spk.mk          # SPK 构建框架
├── packages/                   # 编译输出目录
└── spk/
    └── simple-hello/          # 插件源码
        ├── Makefile           # 插件构建配置
        ├── INFO               # 插件信息文件
        └── src/               # 源代码目录
            ├── simple-server.py    # Python HTTP 服务器
            ├── index.html          # 网页界面
            ├── style.css           # 样式文件
            ├── installer.sh        # 安装脚本
            ├── service.sh          # 服务控制脚本
            └── service-setup.sh    # 服务配置脚本
```

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Make
- Python 3
- 基本的构建工具

### 2. 初始化项目

```bash
# 设置开发环境
make setup
```

### 3. 编译插件

```bash
# 编译 x64 架构的插件
cd spk/simple-hello
make arch-x64-7.2

# 或编译 aarch64 架构的插件
make arch-aarch64-7.2

# 编译所有支持的架构
make all-supported
```

### 4. 查看编译结果

编译完成后，SPK 文件将生成在 `packages/` 目录中：

```bash
ls packages/
# 输出示例: simple-hello_x64-7.2_1.0.0-1.spk
```

## 插件功能

### Simple Hello 插件

这是一个简单的 "Hello World" 插件，包含以下功能：

1. **Web 界面**: 在端口 8080 提供简单的网页界面
2. **状态显示**: 显示插件版本和系统信息
3. **非 Root 运行**: 使用普通用户权限运行
4. **日志记录**: 记录运行状态到 `/tmp/simple-hello.log`

### 访问方式

安装插件后，可通过以下方式访问：
- 浏览器访问: `http://你的群晖IP:8080`
- 在群晖套件中心查看插件状态

## 安装说明

1. 将编译好的 `.spk` 文件上传到群晖
2. 在套件中心选择"手动安装"
3. 选择上传的 SPK 文件
4. 按照向导完成安装

**注意**: 由于使用非 root 权限，某些系统级功能可能受限，这是为了安全考虑的设计。

## 开发说明

### 修改插件

1. 编辑 `spk/simple-hello/src/` 目录下的文件
2. 重新编译: `make arch-x64-7.2`
3. 测试新的 SPK 文件

### 添加新功能

1. 修改 `simple-server.py` 添加新的 HTTP 路由
2. 更新 `index.html` 和 `style.css` 修改界面
3. 如需要，更新 `INFO` 文件中的版本号

### 调试

- 查看安装日志: `/tmp/simple-hello_install.log`
- 查看运行日志: `/tmp/simple-hello.log`
- 检查服务状态: 在群晖套件中心查看

## 技术细节

- **编程语言**: Python 3 (内置 HTTP 服务器)
- **前端**: HTML5 + CSS3 + JavaScript
- **架构支持**: x64, aarch64
- **DSM 版本**: 7.0 - 7.2
- **端口**: 8080 (可在源码中修改)

## 故障排除

### 常见问题

1. **编译失败**: 检查 Make 和 Python 是否正确安装
2. **安装失败**: 确认 DSM 版本兼容性
3. **服务无法启动**: 检查端口 8080 是否被占用
4. **权限错误**: 确认插件使用非 root 用户运行

### 日志位置

- 安装日志: `/tmp/simple-hello_install.log`
- 运行日志: `/tmp/simple-hello.log`
- 设置日志: `/tmp/simple-hello_setup.log`

## 许可证

本项目基于 SynoCommunity/spksrc 框架，遵循相应的开源许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
