@echo off
REM Manual build script for Simple Hello SPK package
REM Simplified version for Windows

echo ========================================
echo Manual Build for Simple Hello SPK
echo ========================================

REM Set variables
set SPK_NAME=simple-hello
set SPK_FILE=%SPK_NAME%_x64-7.2_1.0.0-1.spk
set WORK_DIR=spk\%SPK_NAME%\work-manual
set STAGING_DIR=%WORK_DIR%\staging

REM Clean and create directories
if exist "%WORK_DIR%" rmdir /s /q "%WORK_DIR%"
mkdir "%STAGING_DIR%\var\www"
mkdir "%STAGING_DIR%\scripts"
if not exist "packages" mkdir "packages"

echo Copying files...

REM Copy source files
copy "spk\%SPK_NAME%\src\simple-server.py" "%STAGING_DIR%\var\" >nul
copy "spk\%SPK_NAME%\src\index.html" "%STAGING_DIR%\var\www\" >nul
copy "spk\%SPK_NAME%\src\style.css" "%STAGING_DIR%\var\www\" >nul
copy "spk\%SPK_NAME%\src\service.sh" "%STAGING_DIR%\scripts\" >nul
copy "spk\%SPK_NAME%\src\installer.sh" "%WORK_DIR%\scripts" >nul
copy "spk\%SPK_NAME%\INFO" "%WORK_DIR%\" >nul

echo Creating package archive...

REM Create package.tgz using PowerShell
cd "%STAGING_DIR%"
powershell -Command "Compress-Archive -Path * -DestinationPath '..\package.zip' -Force"
cd ..\..\..

REM Rename to .tgz
move "%WORK_DIR%\package.zip" "%WORK_DIR%\package.tgz" >nul

echo Creating final SPK...

REM Create SPK file
cd "%WORK_DIR%"
powershell -Command "Compress-Archive -Path 'package.tgz','INFO' -DestinationPath '..\..\packages\%SPK_FILE%' -Force"
cd ..\..

echo ========================================
echo Build completed!
echo Package: packages\%SPK_FILE%
echo ========================================

REM Show file info
if exist "packages\%SPK_FILE%" (
    echo File created successfully:
    dir "packages\%SPK_FILE%"
    echo.
    echo Installation instructions:
    echo 1. Upload %SPK_FILE% to your Synology NAS
    echo 2. Open Package Center ^> Manual Install
    echo 3. Select the SPK file and install
    echo 4. Access via http://your-nas-ip:8080
) else (
    echo ERROR: SPK file was not created!
)

pause
