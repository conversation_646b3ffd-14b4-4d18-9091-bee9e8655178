# Makefile for simple-hello SPK package
# Compatible with DSM 7.2, avoiding root permissions

SPK_NAME = simple-hello
SPK_VERS = 1.0.0
SPK_REV = 1
SPK_ICON = src/simple-hello.png

# Package metadata
MAINTAINER = SynoCommunity
DESCRIPTION = Simple Hello World application for Synology DSM 7.2
RELOAD_UI = no
DISPLAY_NAME = Simple Hello
CHANGELOG = "Initial release"

# DSM 7 compatibility
REQUIRED_MIN_VERSION = 7.0
REQUIRED_MAX_VERSION = 7.2-99999

# Architecture support
SUPPORTED_ARCHS = x64 aarch64

# Service configuration - use non-root user
SERVICE_USER = auto
SERVICE_SETUP = src/service-setup.sh
SERVICE_PORT = 8080
SERVICE_PORT_TITLE = Simple Hello Web UI

# No dependencies to avoid complexity
DEPENDS = 

# Installation scripts
INSTALLER_SCRIPT = src/installer.sh
SSS_SCRIPT = src/service.sh

# Package files
COPY_TARGET = nop
POST_STRIP_TARGET = simple-hello_extra_install

# Include common SPK framework
include ../../mk/spksrc.spk.mk

# Custom installation target
.PHONY: simple-hello_extra_install
simple-hello_extra_install:
	@echo "Installing simple-hello files..."
	install -m 755 -d $(STAGING_DIR)/var
	install -m 755 -d $(STAGING_DIR)/var/www
	install -m 644 src/index.html $(STAGING_DIR)/var/www/
	install -m 644 src/style.css $(STAGING_DIR)/var/www/
	install -m 755 src/simple-server.py $(STAGING_DIR)/var/
	@echo "Installation complete"
