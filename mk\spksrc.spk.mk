# SPK build framework - simplified version
# Based on SynoCommunity/spksrc structure

# Default values
SPK_NAME ?= $(notdir $(CURDIR))
SPK_VERS ?= 1.0.0
SPK_REV ?= 1
ARCH ?= x64
DSM_VERSION ?= 7.2

# Directories
WORK_DIR = work-$(ARCH)-$(DSM_VERSION)
STAGING_DIR = $(WORK_DIR)/staging
PACKAGE_DIR = ../../packages

# Package file name
SPK_FILE = $(SPK_NAME)_$(ARCH)-$(DSM_VERSION)_$(SPK_VERS)-$(SPK_REV).spk

# Default targets
.PHONY: all clean arch-% package

# Architecture-specific build
arch-%:
	@echo "Building $(SPK_NAME) for architecture $*"
	@$(MAKE) ARCH=$(word 1,$(subst -, ,$*)) DSM_VERSION=$(word 2,$(subst -, ,$*)) package

# Main package target
package: $(PACKAGE_DIR)/$(SPK_FILE)

$(PACKAGE_DIR)/$(SPK_FILE): prepare_staging
	@echo "Creating SPK package: $(SPK_FILE)"
	@mkdir -p $(PACKAGE_DIR)
	@cd $(STAGING_DIR) && tar czf ../package.tgz *
	@cd $(WORK_DIR) && tar cf ../../$(PACKAGE_DIR)/$(SPK_FILE) package.tgz INFO
	@echo "Package created: $(PACKAGE_DIR)/$(SPK_FILE)"

# Prepare staging directory
prepare_staging: $(STAGING_DIR)
	@echo "Preparing staging directory..."
	@cp INFO $(WORK_DIR)/
	@if [ -f "src/installer.sh" ]; then \
		cp src/installer.sh $(WORK_DIR)/scripts; \
		chmod +x $(WORK_DIR)/scripts; \
	fi
	@if [ -f "src/service.sh" ]; then \
		mkdir -p $(STAGING_DIR)/scripts; \
		cp src/service.sh $(STAGING_DIR)/scripts/; \
		chmod +x $(STAGING_DIR)/scripts/service.sh; \
	fi
	@$(MAKE) $(POST_STRIP_TARGET) || true

$(STAGING_DIR):
	@echo "Creating staging directory: $(STAGING_DIR)"
	@mkdir -p $(STAGING_DIR)
	@mkdir -p $(WORK_DIR)/scripts

# Clean target
clean:
	@echo "Cleaning $(SPK_NAME)..."
	@rm -rf work-*
	@rm -f $(PACKAGE_DIR)/$(SPK_NAME)_*.spk

# No-op target
nop:
	@echo "No-op target executed"

# Help
help:
	@echo "Available targets:"
	@echo "  arch-x64-7.2     - Build for x64 DSM 7.2"
	@echo "  arch-aarch64-7.2 - Build for aarch64 DSM 7.2"
	@echo "  clean            - Clean build artifacts"
	@echo "  help             - Show this help"
