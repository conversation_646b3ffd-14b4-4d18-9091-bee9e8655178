# PowerShell build script for Simple Hello SPK package
# Compatible with DSM 7.2

param(
    [string]$Action = "build"
)

# Configuration
$SPK_NAME = "simple-hello"
$SPK_VERS = "1.0.0"
$SPK_REV = "1"
$ARCH = "x64"
$DSM_VERSION = "7.2"
$SPK_FILE = "${SPK_NAME}_${ARCH}-${DSM_VERSION}_${SPK_VERS}-${SPK_REV}.spk"

# Directories
$WORK_DIR = "spk\$SPK_NAME\work-$ARCH-$DSM_VERSION"
$STAGING_DIR = "$WORK_DIR\staging"
$PACKAGE_DIR = "packages"

Write-Host "========================================" -ForegroundColor Green
Write-Host "Building $SPK_NAME for DSM $DSM_VERSION" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

switch ($Action) {
    "setup" {
        Write-Host "Setting up development environment..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Force -Path $PACKAGE_DIR | Out-Null
        New-Item -ItemType Directory -Force -Path "distrib" | Out-Null
        New-Item -ItemType Directory -Force -Path "toolchain" | Out-Null
        Write-Host "Setup complete!" -ForegroundColor Green
    }
    
    "clean" {
        Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow
        if (Test-Path "spk\$SPK_NAME\work-*") {
            Remove-Item "spk\$SPK_NAME\work-*" -Recurse -Force
        }
        if (Test-Path "$PACKAGE_DIR\${SPK_NAME}_*.spk") {
            Remove-Item "$PACKAGE_DIR\${SPK_NAME}_*.spk" -Force
        }
        Write-Host "Clean complete!" -ForegroundColor Green
    }
    
    "build" {
        Write-Host "Starting build process..." -ForegroundColor Yellow
        
        # Create directories
        New-Item -ItemType Directory -Force -Path $STAGING_DIR | Out-Null
        New-Item -ItemType Directory -Force -Path "$STAGING_DIR\var\www" | Out-Null
        New-Item -ItemType Directory -Force -Path "$STAGING_DIR\scripts" | Out-Null
        New-Item -ItemType Directory -Force -Path "$WORK_DIR\scripts" | Out-Null
        New-Item -ItemType Directory -Force -Path $PACKAGE_DIR | Out-Null
        
        # Copy files
        Write-Host "Copying source files..." -ForegroundColor Cyan
        Copy-Item "spk\$SPK_NAME\INFO" "$WORK_DIR\" -Force
        Copy-Item "spk\$SPK_NAME\src\simple-server.py" "$STAGING_DIR\var\" -Force
        Copy-Item "spk\$SPK_NAME\src\index.html" "$STAGING_DIR\var\www\" -Force
        Copy-Item "spk\$SPK_NAME\src\style.css" "$STAGING_DIR\var\www\" -Force
        Copy-Item "spk\$SPK_NAME\src\service.sh" "$STAGING_DIR\scripts\" -Force
        # Copy-Item "spk\$SPK_NAME\src\installer.sh" "$WORK_DIR\scripts\" -Force
        
        # Create package archive
        Write-Host "Creating package archive..." -ForegroundColor Cyan
        $stagingPath = Resolve-Path $STAGING_DIR
        $packagePath = Join-Path (Resolve-Path $WORK_DIR) "package.zip"
        Compress-Archive -Path "$stagingPath\*" -DestinationPath $packagePath -Force
        
        # Rename to .tgz for compatibility
        $tgzPath = $packagePath -replace "\.zip$", ".tgz"
        Move-Item $packagePath $tgzPath -Force
        
        # Create final SPK (use .zip extension first, then rename)
        Write-Host "Creating SPK package..." -ForegroundColor Cyan
        $workPath = Resolve-Path $WORK_DIR
        $spkZipPath = Join-Path (Resolve-Path $PACKAGE_DIR) "$SPK_FILE.zip"
        $spkPath = Join-Path (Resolve-Path $PACKAGE_DIR) $SPK_FILE
        Compress-Archive -Path "$workPath\package.tgz", "$workPath\INFO" -DestinationPath $spkZipPath -Force

        # Rename to .spk
        if (Test-Path $spkZipPath) {
            Move-Item $spkZipPath $spkPath -Force
        }
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Build completed successfully!" -ForegroundColor Green
        Write-Host "Package created: $PACKAGE_DIR\$SPK_FILE" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        # Show file info
        if (Test-Path "$PACKAGE_DIR\$SPK_FILE") {
            $fileInfo = Get-Item "$PACKAGE_DIR\$SPK_FILE"
            Write-Host "File size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "Installation instructions:" -ForegroundColor Yellow
            Write-Host "1. Upload $SPK_FILE to your Synology NAS" -ForegroundColor White
            Write-Host "2. Open Package Center > Manual Install" -ForegroundColor White
            Write-Host "3. Select the SPK file and install" -ForegroundColor White
            Write-Host "4. Access via http://your-nas-ip:8080" -ForegroundColor White
        } else {
            Write-Host "ERROR: SPK file was not created!" -ForegroundColor Red
        }
    }
    
    default {
        Write-Host "Usage: .\build.ps1 [setup|build|clean]" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Commands:" -ForegroundColor Cyan
        Write-Host "  setup  - Setup development environment" -ForegroundColor White
        Write-Host "  build  - Build the SPK package (default)" -ForegroundColor White
        Write-Host "  clean  - Clean build artifacts" -ForegroundColor White
    }
}
