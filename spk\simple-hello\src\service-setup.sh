#!/bin/bash
# Service setup script for simple-hello
# Configures the service for non-root operation

# Package information
PACKAGE="simple-hello"
DNAME="Simple Hello"

# Paths
INSTALL_DIR="/var/packages/${PACKAGE}/target"
LOG_FILE="/tmp/${PACKAGE}_setup.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

# Setup service configuration
setup_service() {
    log "Setting up ${DNAME} service configuration"
    
    # Create necessary directories
    mkdir -p "${INSTALL_DIR}/var/www" 2>/dev/null || true
    
    # Set executable permissions for scripts
    chmod +x "${INSTALL_DIR}/var/simple-server.py" 2>/dev/null || true
    
    # Create a simple status check
    cat > "${INSTALL_DIR}/var/status.json" << EOF
{
    "package": "${PACKAGE}",
    "name": "${DNAME}",
    "version": "1.0.0",
    "status": "configured",
    "port": 8080,
    "user": "non-root",
    "setup_time": "$(date -Iseconds)"
}
EOF
    
    log "Service configuration completed"
    return 0
}

# Main execution
case $1 in
    setup)
        setup_service
        exit $?
        ;;
    *)
        log "Service setup script called with: $1"
        setup_service
        exit $?
        ;;
esac
