@echo off
REM Windows build script for Simple Hello SPK package
REM Compatible with DSM 7.2

setlocal enabledelayedexpansion

REM Package configuration
set SPK_NAME=simple-hello
set SPK_VERS=1.0.0
set SPK_REV=1
set ARCH=x64
set DSM_VERSION=7.2

REM Directories
set WORK_DIR=spk\%SPK_NAME%\work-%ARCH%-%DSM_VERSION%
set STAGING_DIR=%WORK_DIR%\staging
set PACKAGE_DIR=packages

REM Package file name
set SPK_FILE=%SPK_NAME%_%ARCH%-%DSM_VERSION%_%SPK_VERS%-%SPK_REV%.spk

echo ========================================
echo Building %SPK_NAME% for DSM %DSM_VERSION%
echo ========================================

REM Check command line arguments
if "%1"=="setup" goto setup
if "%1"=="clean" goto clean
if "%1"=="build" goto build
if "%1"=="help" goto help
if "%1"=="" goto build

echo Unknown command: %1
goto help

:setup
echo Setting up development environment...
if not exist "%PACKAGE_DIR%" mkdir "%PACKAGE_DIR%"
if not exist "distrib" mkdir "distrib"
if not exist "toolchain" mkdir "toolchain"
echo Setup complete!
goto end

:clean
echo Cleaning build artifacts...
if exist "spk\%SPK_NAME%\work-*" rmdir /s /q "spk\%SPK_NAME%\work-*"
if exist "%PACKAGE_DIR%\%SPK_NAME%_*.spk" del /q "%PACKAGE_DIR%\%SPK_NAME%_*.spk"
echo Clean complete!
goto end

:build
echo Starting build process...

REM Create work directories
if not exist "%WORK_DIR%" mkdir "%WORK_DIR%"
if not exist "%STAGING_DIR%" mkdir "%STAGING_DIR%"
if not exist "%STAGING_DIR%\var" mkdir "%STAGING_DIR%\var"
if not exist "%STAGING_DIR%\var\www" mkdir "%STAGING_DIR%\var\www"
if not exist "%STAGING_DIR%\scripts" mkdir "%STAGING_DIR%\scripts"
if not exist "%PACKAGE_DIR%" mkdir "%PACKAGE_DIR%"

REM Copy INFO file
copy "spk\%SPK_NAME%\INFO" "%WORK_DIR%\" >nul
if errorlevel 1 (
    echo ERROR: Failed to copy INFO file
    goto error
)

REM Copy source files to staging
echo Copying source files...
copy "spk\%SPK_NAME%\src\simple-server.py" "%STAGING_DIR%\var\" >nul
copy "spk\%SPK_NAME%\src\index.html" "%STAGING_DIR%\var\www\" >nul
copy "spk\%SPK_NAME%\src\style.css" "%STAGING_DIR%\var\www\" >nul
copy "spk\%SPK_NAME%\src\service.sh" "%STAGING_DIR%\scripts\" >nul

REM Copy installer script to work directory
copy "spk\%SPK_NAME%\src\installer.sh" "%WORK_DIR%\scripts" >nul

REM Create package.tgz (using PowerShell for compression)
echo Creating package archive...
powershell -Command "& {cd '%STAGING_DIR%'; Compress-Archive -Path * -DestinationPath '..\package.zip' -Force}"
if errorlevel 1 (
    echo ERROR: Failed to create package archive
    goto error
)

REM Rename zip to tgz for compatibility
move "%WORK_DIR%\package.zip" "%WORK_DIR%\package.tgz" >nul

REM Create final SPK file (tar format simulation using PowerShell)
echo Creating SPK package...
cd /d "%~dp0"
cd "%WORK_DIR%"
if not exist "..\..\%PACKAGE_DIR%" mkdir "..\..\%PACKAGE_DIR%"
powershell -Command "& {Compress-Archive -Path 'package.tgz','INFO' -DestinationPath '..\..\%PACKAGE_DIR%\%SPK_FILE%.zip' -Force}"
if errorlevel 1 (
    echo ERROR: Failed to create SPK package
    goto error
)

REM Rename to .spk
cd /d "%~dp0"
move "%PACKAGE_DIR%\%SPK_FILE%.zip" "%PACKAGE_DIR%\%SPK_FILE%" >nul

echo.
echo ========================================
echo Build completed successfully!
echo Package created: %PACKAGE_DIR%\%SPK_FILE%
echo ========================================
echo.
echo To install on Synology:
echo 1. Upload %SPK_FILE% to your Synology NAS
echo 2. Open Package Center
echo 3. Click "Manual Install"
echo 4. Select the uploaded SPK file
echo 5. Follow the installation wizard
echo.
goto end

:help
echo.
echo Usage: build.bat [command]
echo.
echo Commands:
echo   setup  - Setup development environment
echo   build  - Build the SPK package (default)
echo   clean  - Clean build artifacts
echo   help   - Show this help
echo.
echo Examples:
echo   build.bat setup
echo   build.bat build
echo   build.bat clean
echo.
goto end

:error
echo.
echo ========================================
echo Build failed!
echo ========================================
exit /b 1

:end
echo.
