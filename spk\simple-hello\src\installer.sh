#!/bin/bash
# Installer script for simple-hello SPK package
# Compatible with DSM 7.2, avoiding root operations

# Package information
PACKAGE="simple-hello"
DNAME="Simple Hello"

# Paths
INSTALL_DIR="/var/packages/${PACKAGE}/target"
WEB_DIR="${INSTALL_DIR}/var/www"
LOG_FILE="/tmp/${PACKAGE}_install.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

# Pre-installation checks
preinst() {
    log "Starting pre-installation checks for ${DNAME}"
    
    # Check DSM version
    if [ ! -f /etc.defaults/VERSION ]; then
        log "ERROR: Cannot determine DSM version"
        exit 1
    fi
    
    # Check if port 8080 is available (basic check)
    if netstat -tuln 2>/dev/null | grep -q ":8080 "; then
        log "WARNING: Port 8080 appears to be in use"
        # Don't exit, just warn
    fi
    
    log "Pre-installation checks completed successfully"
    exit 0
}

# Post-installation setup
postinst() {
    log "Starting post-installation setup for ${DNAME}"
    
    # Create necessary directories
    mkdir -p "${WEB_DIR}" 2>/dev/null || true
    mkdir -p "${INSTALL_DIR}/var" 2>/dev/null || true
    
    # Set permissions (non-root friendly)
    if [ -d "${INSTALL_DIR}" ]; then
        # Only set permissions we can actually set
        chmod 755 "${INSTALL_DIR}/var/simple-server.py" 2>/dev/null || true
        chmod 644 "${WEB_DIR}"/*.html 2>/dev/null || true
        chmod 644 "${WEB_DIR}"/*.css 2>/dev/null || true
    fi
    
    log "Post-installation setup completed successfully"
    exit 0
}

# Pre-upgrade
preuninst() {
    log "Starting pre-uninstallation for ${DNAME}"
    
    # Stop the service if running
    if [ -f "${INSTALL_DIR}/var/simple-server.py" ]; then
        pkill -f "simple-server.py" 2>/dev/null || true
    fi
    
    log "Pre-uninstallation completed"
    exit 0
}

# Post-upgrade
postuninst() {
    log "Starting post-uninstallation cleanup for ${DNAME}"
    
    # Clean up temporary files
    rm -f "/tmp/simple-hello.log" 2>/dev/null || true
    rm -f "${LOG_FILE}" 2>/dev/null || true
    
    log "Post-uninstallation cleanup completed"
    exit 0
}

# Main execution
case $1 in
    preinst)
        preinst
        ;;
    postinst)
        postinst
        ;;
    preuninst)
        preuninst
        ;;
    postuninst)
        postuninst
        ;;
    *)
        log "ERROR: Unknown installer action: $1"
        exit 1
        ;;
esac
