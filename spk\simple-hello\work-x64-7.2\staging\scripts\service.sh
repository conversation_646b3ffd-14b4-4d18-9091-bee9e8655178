#!/bin/bash
# Service control script for simple-hello
# Compatible with DSM 7.2, non-root operation

# Package information
PACKAGE="simple-hello"
DNAME="Simple Hello"

# Paths
INSTALL_DIR="/var/packages/${PACKAGE}/target"
PYTHON_SCRIPT="${INSTALL_DIR}/var/simple-server.py"
PID_FILE="/tmp/${PACKAGE}.pid"
LOG_FILE="/tmp/${PACKAGE}.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

# Start service
start_daemon() {
    log "Starting ${DNAME} service"
    
    # Check if already running
    if [ -f "${PID_FILE}" ] && kill -0 "$(cat ${PID_FILE})" 2>/dev/null; then
        log "Service is already running"
        return 0
    fi
    
    # Check if Python script exists
    if [ ! -f "${PYTHON_SCRIPT}" ]; then
        log "ERROR: Python script not found at ${PYTHON_SCRIPT}"
        return 1
    fi
    
    # Start the service
    cd "${INSTALL_DIR}/var" || return 1
    python3 "${PYTHON_SCRIPT}" > "${LOG_FILE}" 2>&1 &
    local pid=$!
    
    # Save PID
    echo $pid > "${PID_FILE}"
    
    # Wait a moment and check if it's still running
    sleep 2
    if kill -0 $pid 2>/dev/null; then
        log "Service started successfully with PID $pid"
        return 0
    else
        log "ERROR: Service failed to start"
        rm -f "${PID_FILE}"
        return 1
    fi
}

# Stop service
stop_daemon() {
    log "Stopping ${DNAME} service"
    
    if [ -f "${PID_FILE}" ]; then
        local pid=$(cat "${PID_FILE}")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                log "Force killing service"
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "${PID_FILE}"
    fi
    
    # Also kill by process name as backup
    pkill -f "simple-server.py" 2>/dev/null || true
    
    log "Service stopped"
    return 0
}

# Check service status
daemon_status() {
    if [ -f "${PID_FILE}" ]; then
        local pid=$(cat "${PID_FILE}")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # Running
        else
            rm -f "${PID_FILE}"
            return 1  # Not running
        fi
    else
        return 1  # Not running
    fi
}

# Main execution
case $1 in
    start)
        start_daemon
        exit $?
        ;;
    stop)
        stop_daemon
        exit $?
        ;;
    status)
        if daemon_status; then
            echo "${DNAME} is running"
            exit 0
        else
            echo "${DNAME} is not running"
            exit 1
        fi
        ;;
    restart)
        stop_daemon
        sleep 2
        start_daemon
        exit $?
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart}"
        exit 1
        ;;
esac
